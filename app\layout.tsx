import "@/styles/globals.css";
import { Metadata, Viewport } from "next";
import { Link } from "@heroui/link";
import clsx from "clsx";

import { Providers } from "./providers";

import { siteConfig } from "@/config/site";
import { fontSans } from "@/config/fonts";
import { Navbar } from "@/components/navbar";
import { OverlayStars } from "@/components/threejs/stars";
import { CursorFollower } from "@/components/cursor-follower";

export const metadata: Metadata = {
  title: {
    default: siteConfig.name,
    template: `%s - ${siteConfig.name}`,
  },
  description: siteConfig.description,
  icons: {
    icon: "/favicon.webp",
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html suppressHydrationWarning lang="en">
      <head />
      <body
        className={clsx(
          "min-h-screen bg-background font-sans antialiased",
          fontSans.variable,
        )}
      >
        <OverlayStars />
        <Providers themeProps={{ attribute: "class", defaultTheme: "light" }}>
          <div className="relative flex flex-col h-screen">
            <Navbar />
            <main className="w-full pt-4 px-6">{children}</main>
            <CursorFollower />
            <footer className="w-full items-center justify-center py-3">
              <div className="flex items-center justify-center py-3">
                <p className="text-default-600">
                  Rosclar Management Onboarding
                </p>
              </div>
              <div className="flex items-center justify-center">
                <Link
                  isExternal
                  className="flex items-center gap-1 text-current"
                  href="https://infini.es"
                  title="infini.es homepage"
                >
                  <span className="text-default-600">
                    Powered by {"(ﾉ◕ヮ◕)ﾉ"}
                  </span>
                  <p className="text-warning">Infini</p>
                  <span className="text-default-600">{"ヽ(◕ヮ◕ヽ)  "}</span>
                </Link>
              </div>
            </footer>
            <div
              className="fixed bottom-14 left-0 bg-zinc-800 text-zinc-100 p-1 pl-4 pr-2 rounded-r text-sm"
              style={{ zIndex: 9999 }}
            >
              {process.env.ENVIRONMENT}
            </div>
          </div>
        </Providers>
      </body>
    </html>
  );
}
