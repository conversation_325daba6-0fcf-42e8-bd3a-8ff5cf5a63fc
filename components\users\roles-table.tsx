import React, { useEffect, useState } from "react";
import {
  <PERSON>ton,
  Card,
  Input,
  Pagination,
  Table,
  TableHeader,
  TableRow,
  TableCell,
  TableBody,
  TableColumn,
  SortDescriptor,
  Spinner,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useAsyncList } from "@react-stately/data";

import { RoleActions } from "./role-actions";
import { RolePermissions } from "./role-permissions";
import { RoleModal } from "./role-modal";

import { useRoles } from "@/hooks/users/useRoles";
import { type Role, type Permission } from "@/types/role";
import { usePermissions } from "@/hooks/users/usePermissions";
import { useAuth } from "@/hooks/auth/useAuth";

interface RoleTableProps {
  canEditRoles?: boolean;
}

export default function RolesTable({ canEditRoles = false }: RoleTableProps) {
  const { roles, loading: isLoading, fetchRoles } = useRoles();
  const {
    permissions,
    loading: loadingPermissions,
    fetchPermissions,
  } = usePermissions();

  const [page, setPage] = useState(1);
  const rowsPerPage = Number(process.env.ROWS_PER_PAGE) || 10;
  const [searchTerm, setSearchTerm] = useState("");
  const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>({
    column: "name",
    direction: "ascending",
  });
  const [currentData, setCurrentData] = useState<Role[]>([]);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [modalMode, setModalMode] = useState<"edit" | "delete" | "create">(
    "edit",
  );
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Helper function to format role permissions for display
  const mapRolesPermissions = (rolesData: Role[]) => {
    return rolesData.map((role) => {
      let permissionNames: string[] = [];

      if (role.permissions && role.permissions.length > 0) {
        // Check permission type and extract names
        if (
          typeof role.permissions[0] === "object" &&
          "name" in role.permissions[0]
        ) {
          permissionNames = (role.permissions as Permission[]).map(
            (p) => p.name,
          );
        } else if (typeof role.permissions[0] === "string") {
          permissionNames = role.permissions as string[];
        }
      }

      return {
        ...role,
        permissions: permissionNames,
      };
    });
  };

  useEffect(() => {
    if (roles && roles.length > 0) {
      const mappedRoles = mapRolesPermissions(roles);

      setCurrentData(mappedRoles);
    }
  }, [roles]);

  useEffect(() => {
    fetchPermissions();
  }, []);

  const list = useAsyncList<Role>({
    async load() {
      return {
        items: currentData,
      };
    },
    async sort({ items: roleItems, sortDescriptor: roleSortDescriptor }) {
      return {
        items: roleItems.sort((roleA: Role, roleB: Role) => {
          const sortColumn = roleSortDescriptor.column as keyof Role;
          const sortDirection =
            roleSortDescriptor.direction === "ascending" ? 1 : -1;

          return (
            sortDirection *
            (roleA[sortColumn] as string).localeCompare(
              roleB[sortColumn] as string,
            )
          );
        }),
      };
    },
  });

  const startIndex = (page - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const paginatedItems = list.items.slice(startIndex, endIndex);
  const pages = Math.ceil(list.items.length / rowsPerPage);

  useEffect(() => {
    fetchRoles();
  }, []);

  useEffect(() => {
    if (sortDescriptor.column) {
      list.sort(sortDescriptor as any);
    }
  }, [sortDescriptor]);

  useEffect(() => {
    list.reload();
  }, [currentData]);

  useEffect(() => {
    setPage(1);
    list.reload();
  }, [searchTerm]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);

    if (value.trim()) {
      // Filter based on role name and permissions
      const filteredItems = roles.filter((role) => {
        const nameMatch = role.name.toLowerCase().includes(value.toLowerCase());
        const permissionMatch = role.permissions.some((permission) => {
          if (typeof permission === "string") {
            return (permission as string)
              .toLowerCase()
              .includes(value.toLowerCase());
          } else if (
            typeof permission === "object" &&
            permission &&
            "name" in permission
          ) {
            return permission.name.toLowerCase().includes(value.toLowerCase());
          }

          return false;
        });

        return nameMatch || permissionMatch;
      });

      const mappedFilteredRoles = mapRolesPermissions(filteredItems);

      setCurrentData(mappedFilteredRoles);
    } else {
      // When search is cleared, reload all roles
      const mappedRoles = mapRolesPermissions(roles);

      setCurrentData(mappedRoles);
    }

    list.reload();
  };

  const handleEdit = (role: Role) => {
    setSelectedRole(role);
    setModalMode("edit");
    setIsModalOpen(true);
  };

  const handleDelete = (role: Role) => {
    setSelectedRole(role);
    setModalMode("delete");
    setIsModalOpen(true);
  };

  const handleModalConfirm = async () => {
    await fetchRoles();
  };

  // Show loading state while permissions are being fetched
  if (loadingPermissions) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner label="Cargando permisos..." />
      </div>
    );
  }

  // Check if user has permission to view roles
  if (!canEditRoles) {
    return (
      <Card className="p-6 mb-4 w-full" radius={"sm"}>
        <div className="text-center text-gray-500">
          <Icon className="mx-auto mb-2" icon="lucide:shield-x" width={48} />
          <p>No tienes permisos para ver los roles del sistema.</p>
        </div>
      </Card>
    );
  }

  return (
    <>
      <Card className="p-2 mb-4 w-full pt-4" radius={"sm"}>
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-4">
          <div className="flex gap-2 w-full sm:w-auto">
            <Input
              className="w-full sm:w-96"
              placeholder="Buscar roles o permisos..."
              startContent={"🔍"}
              value={searchTerm}
              onValueChange={handleSearch}
            />
          </div>
          <div className="flex gap-2 w-full sm:w-auto justify-end">
            <Button
              color="primary"
              isDisabled={!canEditRoles}
              startContent={<Icon icon="lucide:plus" />}
              onPress={() => {
                setSelectedRole(null);
                setModalMode("create");
                setIsModalOpen(true);
              }}
            >
              Agregar Rol
            </Button>
          </div>
        </div>
      </Card>

      <Table
        key={
          isModalOpen
            ? "modal-open" + canEditRoles
            : "modal-closed" + canEditRoles
        }
        aria-label="Tabla de roles"
        bottomContent={
          <div className="flex w-full justify-center">
            <Pagination
              color="primary"
              isCompact={true}
              page={page}
              showControls={true}
              total={pages}
              onChange={(page) => setPage(page)}
            />
          </div>
        }
        removeWrapper={true}
        sortDescriptor={sortDescriptor as any}
        onSortChange={setSortDescriptor}
      >
        <TableHeader>
          <TableColumn key="name" allowsSorting>
            NOMBRE
          </TableColumn>
          <TableColumn key="permissions">PERMISOS</TableColumn>
          <TableColumn key="actions" align="end">
            ACCIONES
          </TableColumn>
        </TableHeader>
        <TableBody
          emptyContent="No se encontraron roles"
          isLoading={isLoading && paginatedItems.length === 0}
          items={paginatedItems}
          loadingContent={<Spinner label="Cargando roles..." />}
        >
          {(item) => (
            <TableRow key={item.id}>
              <TableCell>{item.name}</TableCell>
              <TableCell>
                <RolePermissions permissions={item.permissions as string[]} rolePerms={permissions} />
              </TableCell>
              <TableCell align="right">
                <RoleActions
                  canEditRoles={canEditRoles}
                  role={item}
                  onDelete={handleDelete}
                  onEdit={handleEdit}
                />
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <RoleModal
        isOpen={isModalOpen}
        mode={modalMode}
        role={selectedRole}
        onClose={() => setIsModalOpen(false)}
        onConfirm={handleModalConfirm}
      />
    </>
  );
}
